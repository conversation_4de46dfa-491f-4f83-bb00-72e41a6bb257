<?php
///////////////////////////////////////////////////////////
/////////////SESSION NEEDED TO OBTAIN SHOP ID//////////////
///////////////////////////////////////////////////////////
//$ishopID = $_SESSION["shop"];
//$_SESSION['$ishopID'] = $ishopID;
///////////////////////////////////////////////////////////
//$mishop=$_GET["mshopID"];
$from=$_GET["from"];
$to=$_GET["to"];

// Include invoice details database connection
include_once('cnxn/cnxn_invoicedetails.php');

// Debug: Check invoice database connection
if (!$invoice_connection) {
    echo "<!-- Invoice DB Connection Failed -->";
} else {
    echo "<!-- Invoice DB Connected Successfully -->";

    // Debug: Check if table exists and show sample data
    $test_query = "SELECT COUNT(*) as record_count FROM invoicedetailsfinancialyearcost WHERE IsDeleted = 0 LIMIT 1";
    $test_rs = mysqli_query($invoice_connection, $test_query);
    if ($test_rs) {
        $test_rw = mysqli_fetch_assoc($test_rs);
        echo "<!-- Invoice table record count: " . $test_rw["record_count"] . " -->";
    } else {
        echo "<!-- Invoice table test query failed: " . mysqli_error($invoice_connection) . " -->";
    }

    // Debug: Show available branch codes in invoice table
    $branch_query = "SELECT DISTINCT branc_code, COUNT(*) as count FROM invoicedetailsfinancialyearcost WHERE IsDeleted = 0 GROUP BY branc_code LIMIT 10";
    $branch_rs = mysqli_query($invoice_connection, $branch_query);
    if ($branch_rs) {
        $branch_info = array();
        while($branch_row = mysqli_fetch_assoc($branch_rs)) {
            $branch_info[] = $branch_row["branc_code"] . "(" . $branch_row["count"] . ")";
        }
        echo "<!-- Available branch codes in invoice table: " . implode(", ", $branch_info) . " -->";
    }

    // Debug: Show sample data from invoice table
    $sample_query = "SELECT branc_code, inv_date, Inv_cost, GoldWeight, inv_sales_amt FROM invoicedetailsfinancialyearcost WHERE IsDeleted = 0 LIMIT 3";
    $sample_rs = mysqli_query($invoice_connection, $sample_query);
    if ($sample_rs) {
        echo "<!-- Sample invoice data: -->";
        while($sample_row = mysqli_fetch_assoc($sample_rs)) {
            echo "<!-- Branch: " . $sample_row["branc_code"] . ", Date: " . $sample_row["inv_date"] . ", Cost: " . $sample_row["Inv_cost"] . ", Weight: " . $sample_row["GoldWeight"] . ", Sales: " . $sample_row["inv_sales_amt"] . " -->";
        }
    }
}

$mp="select cost_calculation,order_cost_calculation from conf";
$mp_rs=mysqli_query($connection, $mp);
$mp_rw=mysqli_fetch_assoc($mp_rs);

if($mp_rw["cost_calculation"]=="C")
{
$cc=" Stock Market GP is Calculated from Current Cost ";
$cc .=" - Order Maket GP Calculated from Current Cost";
}
if($mp_rw["cost_calculation"]=="H")
{
$cc=" Stock Market GP is Calculated from Higher Value of Current Cost & Historical Cost ";
$cc .=" - Order Market GP is Calculated from Higher Value of Current Cost & Historical Cost  ";
}



if(isset($_GET["all_branches"]))
{

$q="select shop_id from branch";
$q_rs=mysqli_query($connection, $q);
while($row_user = mysqli_fetch_assoc($q_rs)){
   $branch[] = $row_user["shop_id"];
}
$sp_name="- All Branches";
}
else {
if(isset($_GET["plmtt"]))
{
$q="select shop_id from branch where company='P'";
$q_rs=mysqli_query($connection, $q);
while($row_user = mysqli_fetch_assoc($q_rs)){
   $branch[] = $row_user["shop_id"];
}
$sp_name="- PLMTT Branches";



}
else {

$branch=$_GET['branch'];
$sp_name .="-Branch [";
foreach ($branch as $names)
{
$sp_name .=$names.' ';

}
//
$sp_name .=" ]";
}
}


?>
<script language="javascript">
function printdiv(printpage)
{
var headstr = "<html><head><title>AA</title></head><body>";
var footstr = "</body>";
var newstr = document.all.item(printpage).innerHTML;
var oldstr = document.body.innerHTML;
document.body.innerHTML = headstr+newstr+footstr;
window.print();
document.body.innerHTML = oldstr;
return false;
}
</script>



<!--STARTING CODE ENDS FR PAGINATION-->
<div class="box">
            <div class="box-header">
              
            </div>
            <div class="table-responsive">  


<div id="div_print">

<h3>Net Sales @ Market Cost : Commision From <?=$from?> To <?=$to?> <?=$sp_name?></h3>

<table width="90%" border="0" cellspacing="0" cellpadding="0" class="responsive">
  <tbody>  
 
   <thead>
   
                           <tr>
                  <td ><strong>Shop</strong></td>

       <td align="right"><strong>Net Weight (g)</strong></td>
     <td align="right"><strong>Net Selling Price (Rs.)</strong></td>
<td align="right"><strong>Net Cost (Rs.)&nbsp;&nbsp;</strong></td>
<td align="right"  bgcolor="#00aaff"><strong>SR Weight (g)</strong></td>

<td align="right"  bgcolor="#00aaff"><strong> SR (Rs.)</strong></td>
<td align="right"  bgcolor="#00aaff"><strong>SR Cost (Rs.)&nbsp;</strong></td>
<td align="right" ><strong>GP Value (Rs.)&nbsp;</strong></td>
<td align="right" ><strong>Market GP %</strong></td>
<td align="right" bgcolor="#ffcc99"><strong>Invoice Cost (Rs.)</strong></td>
<td align="right" bgcolor="#ffcc99"><strong>Gold Weight (g)</strong></td>
<td align="right" bgcolor="#ffcc99"><strong>Invoice Sales (Rs.)</strong></td>



                </tr>
                </thead>
 
<!-- HERE we are going to print the list-->
<?php
$sw=0;
$ss=0;
$scost=0;
$tw=0;
$ts=0;
$tc=0;
$tsr=0;
$tsrc=0;
$trw=0;
// Invoice data totals
$tinv_cost=0;
$tinv_gold_weight=0;
$tinv_sales_amt=0;
foreach ($branch as $names)

{

$mishop=trim($names);
$from=trim($_GET["from"]);
$to=trim($_GET["to"]);

$stock='genstock';
$genSTtable="$stock$mishop";

$order='orderstock';
$ordtable="$order$mishop";

$sql="SELECT sum(weight) as weight,sum(amount) as amount,round((sum(amount)-sum(cost))*100/sum(amount),2) as profit,sum(cost) as cost,sales_no from (";

if($mp_rw["cost_calculation"]=="H")
{
$sql .="SELECT sum(weight) as weight,sales_no,sum(amount) as amount,round((sum(amount)-sum(if(current_cost<cost,cost,current_cost)))*100/sum(amount),2) as profit,sum(if(current_cost<cost,cost,current_cost)) as cost  from ".$genSTtable." where out_date is not NULL and out_status='S' ";
}

if($mp_rw["cost_calculation"]=="C")
{
$sql .="SELECT sum(weight) as weight,sales_no,sum(amount) as amount,round((sum(amount)-sum(current_cost))*100/sum(amount),2) as profit,sum(current_cost) as cost  from ".$genSTtable." where out_date is not NULL and out_status='S' ";
}


if(isset($_GET["from"]) and !empty($_GET["from"]))
{
$sql .=" and out_date>='".$from."'";
}

if(isset($_GET["to"]) and !empty($_GET["to"]))
{
$sql .=" and out_date<='".$to."'";
}

$sql .=" UNION ALL ";

if($mp_rw["cost_calculation"]=="H")
{
$sql .="SELECT sum(weight) as weight,sales_no,sum(amount) as amount,round((sum(amount)-sum(if(current_cost<cost AND adv_full=0,cost,current_cost)))*100/sum(amount),2) as profit,sum(if(current_cost<cost AND adv_full=0,cost,current_cost)) as cost  from ".$ordtable." where out_date is not NULL and out_status='S' and (repair is NULL or repair='')";
}


if($mp_rw["cost_calculation"]=="C")
{
$sql .="SELECT sum(weight) as weight,sales_no,sum(amount) as amount,round((sum(amount)-sum(current_cost))*100/sum(amount),2) as profit,sum(current_cost) as cost  from ".$ordtable." where out_date is not NULL and out_status='S' and (repair is NULL or repair='')";
}

if(isset($_GET["from"]) and !empty($_GET["from"]))
{
$sql .=" and out_date>='".$from."'";
}

if(isset($_GET["to"]) and !empty($_GET["to"]))
{
$sql .=" and out_date<='".$to."'";
}

$sql .=" ) as ss  ";

$result=mysqli_query($connection, $sql);
$row=mysqli_fetch_assoc($result);


$sales="sr_detail$mishop";

if($mp_rw["cost_calculation"]=="H")
{
$qt="select ifnull(sum(rweight),0) as rweight,ifnull(sum(ramount),0.00) as ramount,ifnull(sum(rcost),0.00) as rcost from (select ifnull(sum(weight),0) as rweight,ifnull(sum(amount),0.00) as ramount,ifnull(sum(if(current_cost<cost,cost,current_cost)),0.00) as rcost from $sales where  pid!='' and shop_id='".$mishop."' ";
}

if($mp_rw["cost_calculation"]=="C")
{
$qt="select ifnull(sum(rweight),0) as rweight,ifnull(sum(ramount),0.00) as ramount,ifnull(sum(rcost),0.00) as rcost from (select ifnull(sum(weight),0) as rweight,ifnull(sum(amount),0.00) as ramount,ifnull(sum(current_cost),0.00) as rcost from $sales where  pid!='' and shop_id='".$mishop."' ";
}



if(isset($_GET["from"]) and !empty($_GET["from"]))
{
$qt .=" and date(pdate)>='".$from."'";
}

if(isset($_GET["to"]) and !empty($_GET["to"]))
{
$qt .=" and date(pdate)<='".$to."'";
}


$kh="select shop_id from branch where shop_id!='".$mishop."'";
$kh_rs=mysqli_query($connection, $kh);
while($kh_rw = mysqli_fetch_assoc($kh_rs)){
$nshop=$kh_rw["shop_id"];
$nsr="sr_detail$nshop";

if($mp_rw["cost_calculation"]=="H")
{
$qt .=" UNION ALL select ifnull(sum(weight),0) as rweight,ifnull(sum(amount),0.00) as ramount,ifnull(sum(if(current_cost<cost,cost,current_cost)),0.00) as rcost from $nsr where  pid!='' and shop_id='".$mishop."'";
}

if($mp_rw["cost_calculation"]=="C")
{
$qt .=" UNION ALL select ifnull(sum(weight),0) as rweight,ifnull(sum(amount),0.00) as ramount,ifnull(sum(current_cost),0.00) as rcost from $nsr where  pid!='' and shop_id='".$mishop."'";
}


if(isset($_GET["from"]) and !empty($_GET["from"]))
{
$qt .=" and date(pdate)>='".$from."'";
}

if(isset($_GET["to"]) and !empty($_GET["to"]))
{
$qt .=" and date(pdate)<='".$to."'";
}


}
$qt .=" ) as mm";



$qt_rs=mysqli_query($connection,$qt);
$qt_rw=mysqli_fetch_assoc($qt_rs);

$tsr=$tsr+$qt_rw["ramount"];
$tsrc=$tsrc+$qt_rw["rcost"];
$tw=$tw+($row["weight"]-$qt_rw["rweight"]);
$ts=$ts+($row["amount"]-$qt_rw["ramount"]);
$tc=$tc+($row["cost"]-$qt_rw["rcost"]);

$gpp=(($row["amount"]-$qt_rw["ramount"])-($row["cost"]-$qt_rw["rcost"]))*100/($row["amount"]-$qt_rw["ramount"]);
$profit=(($row["amount"]-$qt_rw["ramount"])-($row["cost"]-$qt_rw["rcost"]));

// Debug: Test if any data exists for this branch without date filter
$test_branch_query = "SELECT COUNT(*) as count, MIN(inv_date) as min_date, MAX(inv_date) as max_date
FROM invoicedetailsfinancialyearcost
WHERE branc_code = '".$mishop."' AND IsDeleted = 0";
$test_branch_rs = mysqli_query($invoice_connection, $test_branch_query);
if ($test_branch_rs) {
    $test_branch_rw = mysqli_fetch_assoc($test_branch_rs);
    echo "<!-- Branch $mishop: Records=" . $test_branch_rw["count"] . ", Date Range=" . $test_branch_rw["min_date"] . " to " . $test_branch_rw["max_date"] . " -->";
}

// Debug: Test with different date formats
$test_date_query = "SELECT COUNT(*) as count FROM invoicedetailsfinancialyearcost
WHERE branc_code = '".$mishop."' AND IsDeleted = 0
AND inv_date >= '2021-04-01' AND inv_date <= '2022-03-31'";
$test_date_rs = mysqli_query($invoice_connection, $test_date_query);
if ($test_date_rs) {
    $test_date_rw = mysqli_fetch_assoc($test_date_rs);
    echo "<!-- Branch $mishop with YYYY-MM-DD format: Records=" . $test_date_rw["count"] . " -->";
}

// Query invoice details for this branch
$inv_query = "SELECT
    IFNULL(SUM(Inv_cost), 0) as total_inv_cost,
    IFNULL(SUM(GoldWeight), 0) as total_gold_weight,
    IFNULL(SUM(inv_sales_amt), 0) as total_inv_sales_amt
FROM invoicedetailsfinancialyearcost
WHERE branc_code = '".$mishop."'
AND IsDeleted = 0";

if(isset($_GET["from"]) and !empty($_GET["from"]))
{
    // Try different date format
    $from_formatted = str_replace('/', '-', $from);
    $inv_query .= " AND inv_date >= '".$from_formatted."'";
}

if(isset($_GET["to"]) and !empty($_GET["to"]))
{
    // Try different date format
    $to_formatted = str_replace('/', '-', $to);
    $inv_query .= " AND inv_date <= '".$to_formatted."'";
}

$inv_rs = mysqli_query($invoice_connection, $inv_query);

// Debug: Check if query executed successfully
if (!$inv_rs) {
    echo "<!-- Invoice Query Error for branch $mishop: " . mysqli_error($invoice_connection) . " -->";
    echo "<!-- Query: $inv_query -->";
    $inv_rw = array("total_inv_cost" => 0, "total_gold_weight" => 0, "total_inv_sales_amt" => 0);
} else {
    $inv_rw = mysqli_fetch_assoc($inv_rs);
    // Debug: Show query results
    echo "<!-- Branch $mishop Invoice Data: Cost=" . $inv_rw["total_inv_cost"] . ", Weight=" . $inv_rw["total_gold_weight"] . ", Sales=" . $inv_rw["total_inv_sales_amt"] . " -->";
    echo "<!-- Query: $inv_query -->";
}

// Add to totals
$tinv_cost += $inv_rw["total_inv_cost"];
$tinv_gold_weight += $inv_rw["total_gold_weight"];
$tinv_sales_amt += $inv_rw["total_inv_sales_amt"];

?>
      <tr>
    <td>
<a href="admin_index.php?from=<?=$from?>&to=<?=$to?>&p=sales_salesman_mkt_perform&m=general&mshopID=<?=$mishop?>&button=Search"><?=$mishop?></a>

</td>
    
    <td align="right" ><?=number_format(($row["weight"]-$qt_rw["rweight"]),3)?>
    </td>
	<td align="right" ><?=number_format(($row["amount"]-$qt_rw["ramount"]),2)?></td>
<td align="right" ><?=number_format(($row["cost"]-$qt_rw["rcost"]),2)?>&nbsp;&nbsp;</td>


<?php

?>
<td align="right" bgcolor="#b3e6ff">
<?=number_format($qt_rw["rweight"],3)?>

</td>
<td align="right" bgcolor="#b3e6ff">
<?=number_format($qt_rw["ramount"],2)?>
</td>
<td align="right" bgcolor="#b3e6ff"><?=number_format($qt_rw["rcost"],2)?>&nbsp;</td>
<td align="right"><?=number_format($profit,2)?></td>

<td align="right"><?=number_format($gpp,2)?></td>
<td align="right" bgcolor="#ffe6cc"><?=number_format($inv_rw["total_inv_cost"],2)?></td>
<td align="right" bgcolor="#ffe6cc"><?=number_format($inv_rw["total_gold_weight"],3)?></td>
<td align="right" bgcolor="#ffe6cc"><?=number_format($inv_rw["total_inv_sales_amt"],2)?></td>



    </tr>
 <?php
$sw=$sw+$row["weight"];
$ss=$ss+$row["amount"];
$scost=$scost+$row["cost"];
$trw=$trw+$qt_rw["rweight"];
   }
 
   ?>  

   </tbody>
 <tr>
    <td colspan="4">&nbsp;</td>
<td colspan="3" bgcolor="#b3e6ff" ></td>
<td></td>
<td></td>
<td colspan="3" bgcolor="#ffe6cc"></td>

    </tr>



  <tr>

<td><strong>Total.</strong></td>

    <td align="right"><strong><?=number_format($tw,3)?></strong>
    </td>
        <td align="right"><strong><?=number_format($ts,2)?></strong></td>
<td align="right"><strong><?=number_format($tc,2)?></strong>&nbsp;&nbsp;</td>
<td align="right" bgcolor="#b3e6ff"><strong><?=number_format($trw,2)?></strong></td>
<td align="right" bgcolor="#b3e6ff"><strong><?=number_format($tsr,2)?></strong></td>
<td align="right" bgcolor="#b3e6ff"><strong><?=number_format($tsrc,2)?></strong>&nbsp;</td>
<td align="right"  ><strong>
<?php
$tgp=($ts-$tc)*100/$ts;

echo number_format($ts-$tc,2);
?>
</td><td align="right">
 <?=round($tgp,2)?></strong></td>
<td align="right" bgcolor="#ffe6cc"><strong><?=number_format($tinv_cost,2)?></strong></td>
<td align="right" bgcolor="#ffe6cc"><strong><?=number_format($tinv_gold_weight,3)?></strong></td>
<td align="right" bgcolor="#ffe6cc"><strong><?=number_format($tinv_sales_amt,2)?></strong></td>


    </tr>
<tr><td colspan="15">&nbsp;</td></tr>
<?php
$g="select now() as time";
$g_rs=mysqli_query($connection, $g);
$g_rw=mysqli_fetch_assoc($g_rs);
$time=$g_rw["time"];
?>
<tr><td colspan="12">* Cost, Sr Cost and Gross Profit is Calculated on Market Gold Cost<br>* <?=$cc?></td></tr>

<tr><td colspan="12">*
If order advance is fully paid 'Higher Value of Current Cost & Historical Cost ' will not implented. In such case current cost will calculated from order date Gold Rate.
</td></tr>


<tr><td colspan="12">Printed By <?=$_SESSION["user_name"]?> on <?=$time?></td></tr>
<tr><td colspan="12"><hr style="width: 100%; color: black; height: 1px; background-color:black;" /></td></tr>
 <tr><td colspan="12">End Of Report </td></tr>
</table>
</div>
<?php  ?>

<form method="post" action="#">
<input name="b_print" type="button"   onClick="printdiv('div_print');" value=" Print " class="btn btn-info pull-left">
</form>


</div>
</div>
